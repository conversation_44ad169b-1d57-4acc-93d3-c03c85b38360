<template>
  <div class="lesson-header">
    <div class="lesson-breadcrumb">
      <router-link :to="{ name: 'AwarenessCoursePreview', params: { course_id: courseId } }" class="breadcrumb-link"> <i class="fa fa-arrow-left"></i> Course Overview </router-link>
      <span class="breadcrumb-separator">/</span>
      <span class="breadcrumb-current">{{ lesson.title }}</span>
    </div>
    <div class="lesson-status-bar">
      <div class="lesson-type-badge" :class="lesson.is_quiz === '1' ? 'quiz-badge' : 'lesson-badge'">
        <i class="fa" :class="lesson.is_quiz === '1' ? 'fa-question-circle' : 'fa-play-circle'"></i>
        <span>{{ lesson.is_quiz === '1' ? 'Quiz' : 'Lesson' }}</span>
      </div>
      <div class="completion-status" v-if="lesson.completed === 1"> <i class="fa fa-check-circle"></i> <span>Completed</span> </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  lesson: { type: Object, required: true },
  courseId: { type: String, required: true }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/LessonPreview/LessonHeader.scss';
</style>