

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.topics-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Topic Accordion */
.topic-item {
  margin-bottom: 0.5rem;
  animation: fadeInUp 0.4s ease-out;
}

.topic-item:nth-child(1) { animation-delay: 0.1s; }
.topic-item:nth-child(2) { animation-delay: 0.2s; }
.topic-item:nth-child(3) { animation-delay: 0.3s; }
.topic-item:nth-child(4) { animation-delay: 0.4s; }

.topic-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  background: white;
}

.topic-header:hover {
  background: #f8fafc;
  border-color: #059669;
}

.topic-header.active {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(4, 120, 87, 0.1));
  border-color: #059669;
}

.topic-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.topic-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #059669, #047857);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.topic-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.topic-lessons-count {
  font-size: 0.75rem;
  color: #6b7280;
}

.topic-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.topic-progress {
  font-size: 0.75rem;
  color: #059669;
  font-weight: 600;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #6b7280;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

/* Topic Lessons */
.topic-lessons {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.topic-lessons.expanded {
  max-height: 500px;
}

.no-lessons {
  padding: 1rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.lessons-list {
  padding: 0.5rem 0;
}

.lesson-item {
  margin-bottom: 0.25rem;
}

.lesson-link,
.lesson-locked {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  border-left: 3px solid transparent;
}

.lesson-link:hover {
  background: #f8fafc;
  border-left-color: #059669;
}

.lesson-item.current .lesson-link {
  background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(4, 120, 87, 0.1));
  border-left-color: #059669;
  color: #059669;
}

.lesson-item.completed .lesson-status i {
  color: #059669;
}

.lesson-item.locked .lesson-locked {
  opacity: 0.6;
  cursor: not-allowed;
}

.lesson-status {
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lesson-status i {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.lesson-content {
  flex: 1;
  min-width: 0;
}

.lesson-title {
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.lesson-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.lesson-type {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6b7280;
}

.lesson-type.locked {
  color: #ef4444;
}

.lesson-indicator {
  opacity: 0;
  transition: opacity 0.3s ease;
  color: #059669;
}

.lesson-item.current .lesson-indicator {
  opacity: 1;
}

/* Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar Styling */
.topics-container::-webkit-scrollbar {
  width: 6px;
}

.topics-container::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.topics-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.topics-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus States for Accessibility */
.topic-header:focus,
.lesson-link:focus {
  outline: 2px solid #059669;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .course-sidebar {
    position: static;
    max-height: none;
    order: 2;
  }
}

@media (max-width: 768px) {
  .sidebar-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .topics-container {
    padding: 0.75rem;
  }

  .topic-header {
    padding: 0.75rem;
  }

  .topic-title {
    font-size: 0.875rem;
  }

  .lesson-link,
  .lesson-locked {
    padding: 0.625rem 0.75rem;
  }
}

@media (max-width: 480px) {
  .sidebar-title {
    font-size: 1rem;
  }

  .topic-header {
    padding: 0.625rem;
  }

  .topic-icon {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .topic-title {
    font-size: 0.8125rem;
  }

  .topic-lessons-count {
    font-size: 0.6875rem;
  }

  .lesson-link,
  .lesson-locked {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .lesson-title {
    font-size: 0.875rem;
  }

  .lesson-type {
    font-size: 0.6875rem;
  }
}
