.course-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  animation: slideInUp 0.6s ease-out;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.course-card.completed-course {
  border: 2px solid #059669;
  background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 1.5rem;
  position: relative;
  z-index: 2;
}

.course-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
}

.status-icon.completed {
  background: linear-gradient(135deg, #059669, #047857);
}

.status-icon.in-progress {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-icon.not-started {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.status-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
}

.progress-badge {
  background: rgba(5, 150, 105, 0.1);
  color: #059669;
  padding: 0.375rem 0.875rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 700;
}

.card-content {
  padding: 1.5rem;
  position: relative;
  z-index: 2;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.card-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.meta-item i {
  width: 16px;
  color: #9ca3af;
}

.card-footer {
  padding: 1.5rem 1.5rem 1.5rem;
  position: relative;
  z-index: 2;
}

.continue-btn,
.download-cert-btn,
.review-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.875rem 1.25rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.continue-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.continue-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  color: white;
  text-decoration: none;
}

.download-cert-btn {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
}

.download-cert-btn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.review-btn {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.review-btn:hover {
  background: linear-gradient(135deg, #4b5563, #374151);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
  color: white;
  text-decoration: none;
}

.completion-ring {
  position: absolute;
  bottom: 7rem;
  right: 1rem;
  z-index: 3;
}

.progress-circle {
  position: relative;
  width: 50px;
  height: 50px;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: #e5e7eb;
  stroke-width: 3;
}

.circle {
  fill: none;
  stroke: #059669;
  stroke-width: 3;
  stroke-linecap: round;
  animation: progress 1s ease-in-out;
}

@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  color: #059669;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .card-content {
    padding: 1.25rem;
  }
  
  .card-footer {
    padding: 0 1.25rem 1.25rem;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 1rem;
  }
  
  .card-header {
    padding: 1rem 1rem 0;
  }
  
  .card-footer {
    padding: 0 1rem 1rem;
  }
  
  .course-meta {
    gap: 0.375rem;
  }
  
  .meta-item {
    font-size: 0.8125rem;
  }
}

/* Focus states for accessibility */
.continue-btn:focus,
.download-cert-btn:focus,
.review-btn:focus {
  outline: 2px solid #059669;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .course-card {
    border-width: 2px;
  }
  
  .status-icon {
    filter: contrast(1.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .course-card {
    animation: none;
  }
  
  .continue-btn,
  .download-cert-btn,
  .review-btn {
    transition: none;
  }
  
  @keyframes progress {
    from, to {
      stroke-dasharray: 0 100;
    }
  }
}

/* Print styles */
@media print {
  .course-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .continue-btn,
  .download-cert-btn,
  .review-btn {
    background: white !important;
    color: black !important;
    border: 1px solid black;
  }
}
