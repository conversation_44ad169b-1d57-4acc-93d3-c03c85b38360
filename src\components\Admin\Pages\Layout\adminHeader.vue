<template>
    <div class="header">
        <nav class="navbar navbar-expand-lg bg-white border-bottom py-3 px-2 px-lg-5">
            <div class="container-fluid">
                <a class="navbar-brand MotLogo" href="https://raidot.ai" target="_blank" rel="noopener noreferrer">Rai<span class="text-danger">DOT</span></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fa fa-fw fa-2x fa-bars"></i>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                        <li class="nav-item pe-3">
                            <router-link class="nav-link" :to="{name: 'AdminDashboard'}"><strong>Dashboard</strong></router-link>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="dropdown"><strong>Features</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Management</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Users'}">Users</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Workshops'}">Workshops</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Certification</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'EvaluationCertification'}">Evaluation Certification</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'ParticipationCertification'}">Participation Certification</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AwarenessCertification'}">Awareness Certification</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown"><strong>Risk Evaluation</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Evaluation Sectors</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'Sectors'}">Select an Industry</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'RiskFactors'}">Risk Factors</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Questions</strong></li>
                            <!--<li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'cgptQuestionnaires'}">ChatGPt Evaluation</router-link></li>-->
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'ntQuestionnaires'}">Non Technical</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etQuestionnaires'}">General Technical</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etaQuestionnaires'}">Specific Application</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a class="nav-link dropdown-toggle" href="javascript:void(0)" data-bs-toggle="dropdown"><strong>Fair Decision Analysis</strong></a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2"><strong>Fair Decision</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'FdSectors'}">Select an Industry</router-link></li>
                                <li class="text-secondary p-3 bg-light border-bottom border-top mb-2"><strong>Questions</strong></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'fdQuestionnaires'}">General Fair Decision</router-link></li>
                                <li class="m-2"><router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'etaFdQuestionnaires'}">Specific Industry Fair Decision</router-link></li>
                            </ul>
                        </li>
                        <li class="nav-item px-3">
                            <router-link class="nav-link" :to="{name: 'Awareness'}"><strong>Awareness</strong></router-link>
                        </li>
                        <li class="nav-item px-3">
                            <router-link class="nav-link" :to="{name: 'AdminPricingList'}"><strong>Pricing</strong></router-link>
                        </li>
                        <li class="nav-item px-3 dropdown">
                            <a href="javascript:void(0)" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                                <strong>Consultancy</strong>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow m-0 p-0" style="min-width: 250px">
                                <li class="text-secondary p-3 bg-light border-bottom mb-2">
                                    <strong>Management</strong>
                                </li>
                                <li class="m-2">
                                    <router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AdminConsultancySlots'}">
                                        Time Slots
                                    </router-link>
                                </li>
                                <li class="m-2">
                                    <router-link class="dropdown-item rounded-pill mb-1" :to="{name: 'AdminConsultancyBookings'}">
                                        Bookings
                                    </router-link>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <div class="d-flex" role="search">
                        <div class="profile">
                            <div class="dropdown">
                                <div class="profile-info dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="profile-avatar">
                                        <img :src="'/img/user.png'" alt="">
                                    </div>
                                </div>
                                <ul class="dropdown-menu dropdown-menu-lg-end p-3 shadow-lg" style="min-width: 250px">
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminProfile'}">Profile</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminEditProfile'}">Update Profile</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminChangePassword'}">Change Password</router-link></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminNotificationSettings'}">Notification Settings</router-link></li>
                                    <li><router-link class="dropdown-item rounded-pill text-center mb-1" :to="{name: 'AdminAccountSettings'}">Account Settings</router-link></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="btn btn-sm btn-danger w-100 rounded-pill" href="javascript:void(0)" @click="Logout">Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>
</template>
<script>


import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    data() {
        return {
            profile: localStorage.getItem('AdminInfo'),
        }
    },
    computed: { },
    methods: {
        ProfileOption(){
            $('.profile-option').toggleClass('active')
        },

        getMe: function () {
            ApiService.POST(ApiRoutes.AdminProfile, {}, (res) => {
                this.loading = false;
                if (parseInt(res.status) === 200) {
                    this.profile = res.data
                }
            })
        },
        langOption(){
            $('.lang-option').toggleClass('active')
        },
        setLang: function(lang) {
            if(window.localStorage.getItem('lang') != lang) {
                window.localStorage.setItem('lang', lang)
                location.reload();
            }

        },
        toggleMenu: function () {
            this.menuMini =  this.menuMini == false ? true : false;
            $('.sidenav').toggleClass('mini');
            $('.sidebar-toggler').toggleClass('active not-active');
            $('.main_section').toggleClass('full');
            if($('.sidenav').hasClass('active')){
                $('.sidenav').removeClass('active')
            }else{
                $('.sidenav').addClass('active')
            }
        },
        formInit(){
            setTimeout(() => {
                if (window.screen.width < 1025) {
                    $('.sidenav').toggleClass('mini');
                    $('.main_section').toggleClass('full');
                }
            })

            // Store references to event handlers for cleanup
            this.outsideClickHandler1 = (e) => {
                const container = $('.outsideClick');
                // Only handle if the container exists and the click is not on a dropdown or dropdown-related element
                if (container.length > 0 &&
                    !$(e.target).closest('.dropdown').length &&
                    !$(e.target).closest('.dropdown-menu').length &&
                    !$(e.target).hasClass('dropdown-toggle')) {
                    if (!container.is(e.target) && container.has(e.target).length === 0) {
                        container.removeClass('active');
                    }
                }
            };

            this.outsideClickHandler2 = (e) => {
                const container = $('.outsideClick2');
                // Only handle if the container exists and the click is not on a dropdown or dropdown-related element
                if (container.length > 0 &&
                    !$(e.target).closest('.dropdown').length &&
                    !$(e.target).closest('.dropdown-menu').length &&
                    !$(e.target).hasClass('dropdown-toggle')) {
                    if (!container.is(e.target) && container.has(e.target).length === 0) {
                        container.removeClass('active');
                    }
                }
            };

            $(document).on('mouseup', this.outsideClickHandler1);
            $(document).on('mouseup', this.outsideClickHandler2);

            // Ensure Bootstrap dropdowns are properly initialized
            this.initializeDropdowns();
        },

        initializeDropdowns() {
            // Initialize all dropdowns in the header
            this.$nextTick(() => {
                const dropdownElements = document.querySelectorAll('.header .dropdown-toggle');
                dropdownElements.forEach(element => {
                    if (!element.hasAttribute('data-bs-initialized')) {
                        new window.bootstrap.Dropdown(element);
                        element.setAttribute('data-bs-initialized', 'true');
                    }
                });
            });
        },

        Logout() {
            this.logoutLoading = true;
            ApiService.GET(ApiRoutes.AdminLogout, (res) => {
                this.logoutLoading = false;
                if (parseInt(res.status) === 200) {
                    localStorage.removeItem('AdminJwtToken');
                    localStorage.removeItem('AdminInfo');
                    this.$router.push({name: 'AdminLogin'});
                }
            });
        },

        cleanup() {
            // Remove event listeners to prevent memory leaks
            if (this.outsideClickHandler1) {
                $(document).off('mouseup', this.outsideClickHandler1);
            }
            if (this.outsideClickHandler2) {
                $(document).off('mouseup', this.outsideClickHandler2);
            }
        },
    },
    created() {},
    mounted() {
        this.getMe()
        this.formInit()
    },
    beforeUnmount() {
        this.cleanup()
    }
}
</script>
