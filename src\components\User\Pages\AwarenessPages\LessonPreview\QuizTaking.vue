<template>
  <div class="quiz-taking">
    <div class="quiz-header">
      <h1 class="quiz-title">{{ lesson.title }}</h1>
      <div class="quiz-progress">
        <div class="progress-info"><span class="question-counter">Question {{ currentQuestionIndex + 1 }} of {{ questions.length }}</span></div>
        <div class="progress-bar"><div class="progress-fill" :style="{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }"></div></div>
      </div>
    </div>

    <div v-if="questions.length > 0" class="quiz-content">
      <div v-for="(question, index) in questions" :key="index" class="question-slide" v-show="currentQuestionIndex === index">
        <div class="question-card">
          <div class="question-header">
            <div class="question-number-badge">{{ index + 1 }}</div>
            <h2 class="question-text">{{ question.question }}</h2>
          </div>
          
          <div class="options-container">
            <div class="option-item" v-for="optionNum in 4" :key="optionNum">
              <label :for="`option_${optionNum}_${index}`" class="option-label" :class="{ 'selected': question.answer === optionNum.toString() }">
                <input class="option-input" type="radio" name="answer" :value="optionNum" :id="`option_${optionNum}_${index}`" @change="question.answer = optionNum.toString()" :checked="question.answer === optionNum.toString()">
                <div class="option-indicator"><span class="option-letter">{{ String.fromCharCode(64 + optionNum) }}</span></div>
                <span class="option-text">{{ question[`option_${optionNum}`] }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="quiz-navigation">
      <button @click="emit('navigate', 'previous')" class="nav-btn prev-btn" :disabled="currentQuestionIndex === 0" :class="{ 'disabled': currentQuestionIndex === 0 }"> <i class="fa fa-chevron-left"></i>Previous </button>
      <div class="nav-indicators"><div v-for="(question, index) in questions" :key="index" class="nav-dot" :class="{ 'active': index === currentQuestionIndex, 'answered': question.answer, 'disabled': !questions[currentQuestionIndex]?.answer }" @click="!questions[currentQuestionIndex]?.answer ? null : emit('navigate', 'goTo', index)"></div></div>
      <button v-if="currentQuestionIndex < (questions.length - 1)" @click="emit('navigate', 'next')" class="nav-btn next-btn" :disabled="!questions[currentQuestionIndex]?.answer" :class="{ 'disabled': !questions[currentQuestionIndex]?.answer }">Next<i class="fa fa-chevron-right"></i></button>
      <button v-else @click="emit('submit')" class="nav-btn submit-btn" :disabled="!allQuestionsAnswered" :class="{ 'disabled': !allQuestionsAnswered }"><i class="fa fa-check"></i>Submit Quiz</button>
    </div>
  </div>
</template>

<script setup>
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';

defineProps({
  lesson: { type: Object, required: true },
  questions: { type: Array, required: true },
  currentQuestionIndex: { type: Number, required: true },
  courseId: { type: String, required: true },
  topicId: { type: String, required: true },
  lessonId: { type: String, required: true }
});

const emit = defineEmits(['submit', 'navigate']);
const { allQuestionsAnswered } = useAwarenessActions();
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/LessonPreview/QuizTaking.scss';
</style>