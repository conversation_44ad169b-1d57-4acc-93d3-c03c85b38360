.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 24px;
  border: 1px solid #e5e7eb;
}

.empty-illustration {
  position: relative;
  margin-bottom: 2rem;
  display: inline-block;
}

.main-icon {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  position: relative;
  z-index: 1;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.element {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 3s ease-in-out infinite;
}

.element-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 20%;
  animation-delay: -1s;
}

.element-2 {
  width: 16px;
  height: 16px;
  top: 60%;
  right: 20%;
  animation-delay: -2s;
}

.element-3 {
  width: 12px;
  height: 12px;
  bottom: 20%;
  left: 30%;
  animation-delay: -0.5s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.empty-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.empty-description {
  color: #6b7280;
  font-size: 1.125rem;
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto 2.5rem;
}

.empty-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary.large {
  padding: 1.125rem 2.5rem;
  font-size: 1.125rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid;
}

.btn-primary.large:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  opacity: 0.8;
}

.btn-secondary {
  background: white;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: 2px solid;
}

.btn-secondary:hover {
  background: #f8fafc;
  transform: translateY(-2px);
  text-decoration: none;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .empty-state {
    padding: 3rem 1.5rem;
  }
  
  .main-icon {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }
  
  .empty-title {
    font-size: 1.5rem;
  }
  
  .empty-description {
    font-size: 1rem;
  }
  
  .empty-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-primary.large {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}
