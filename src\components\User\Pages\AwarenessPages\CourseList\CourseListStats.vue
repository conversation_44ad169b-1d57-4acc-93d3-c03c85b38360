<template>
  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in stats" :key="stat.label">
        <div class="stat-icon"><i :class="stat.icon"></i></div>
        <div class="stat-content">
          <div class="stat-number">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  coursesCount: { type: Number, required: true }
});

const stats = computed(() => [
  { icon: 'fa fa-book', value: props.coursesCount, label: 'Available Courses' },
  { icon: 'fa fa-clock-o', value: 'Self-paced', label: 'Learning Mode' },
  { icon: 'fa fa-certificate', value: 'Free', label: 'Certification' },
  { icon: 'fa fa-graduation-cap', value: 'Expert', label: 'Level Training' }
]);
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CourseList/CourseListStats.scss';
</style>
