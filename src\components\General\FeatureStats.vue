<template>
  <div class="section-header">
    <h2 class="section-title">{{ title }}</h2>
    <div class="feature-stats">
      <div class="stat-item" v-for="stat in stats" :key="stat.label">
        <span class="stat-number" :style="{ color: primaryColor }">{{ stat.value }}</span>
        <span class="stat-label">{{ stat.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: { type: String, required: true },
  stats: { type: Array, required: true },
  primaryColor: { type: String, default: '#059669' }
});
</script>

<style scoped>
@import '@/assets/scss/general/FeatureStats.scss';
</style>
