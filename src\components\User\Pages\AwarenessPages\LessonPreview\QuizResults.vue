<template>
  <div class="quiz-results">
    <div class="results-card">
      <div class="results-header">
        <div class="score-circle" :class="getScoreClass(quizResult.score)">
          <div class="score-value">{{ quizResult.score }}%</div>
          <div class="score-label">Score</div>
        </div>
        <div class="results-info">
          <h2 class="results-title">{{ getScoreMessage(quizResult.score) }}</h2>
          <p class="results-description">{{ getScoreDescription(quizResult.score) }}</p>
        </div>
      </div>

      <div class="answers-grid">
        <h3 class="answers-title">Your Answers</h3>
        <div class="answers-overview">
          <div v-for="(answer, index) in quizResult.submissions" :key="index" class="answer-indicator" :class="answer.correct === 1 ? 'correct' : 'incorrect'" :title="`Question ${index + 1}: ${answer.correct === 1 ? 'Correct' : 'Incorrect'}`">
            <span class="question-number">{{ index + 1 }}</span>
            <i class="fa" :class="answer.correct === 1 ? 'fa-check' : 'fa-times'"></i>
          </div>
        </div>
      </div>
      
      <div class="quiz-actions">
        <div v-if="lesson.is_final_exam === '0'" class="standard-quiz-actions">
          <button @click="emit('retake')" class="retake-btn"><i class="fa fa-refresh"></i>Retake Quiz</button>
        </div>
        
        <div v-else class="final-exam-actions">
          <div v-if="quizResult.score >= 80" class="passing-actions">
            <div class="congratulations"><i class="fa fa-trophy"></i><span>Congratulations! You passed the final exam.</span></div>
            <button @click="emit('downloadCertificate')" class="certificate-btn"> <i class="fa fa-certificate"></i>Download Certificate</button>
          </div>
          
          <div v-else class="failing-actions">
            <div class="try-again-message">
              <i class="fa fa-info-circle"></i>
              <div class="message-content">
                <h4>Almost there!</h4>
                <p>You need to score at least 80% to get the certificate. Keep trying!</p>
              </div>
            </div>
            <button @click="emit('retake')" class="retake-btn"> <i class="fa fa-refresh"></i>Retake Final Exam</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';

defineProps({
  quizResult: { type: Object, required: true },
  lesson: { type: Object, required: true },
  courseId: { type: String, required: true }
});

const emit = defineEmits(['retake', 'downloadCertificate']);
const { getScoreClass, getScoreMessage, getScoreDescription } = useAwarenessActions();
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/LessonPreview/QuizResults.scss';
</style>
