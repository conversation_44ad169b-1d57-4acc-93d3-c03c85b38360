/* Course Cards */
.course-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
    cursor: pointer;
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.course-card.list-view {
    display: flex;
    align-items: center;
    padding: 1.5rem;
}

.course-card.list-view .course-image {
    width: 120px;
    height: 80px;
    flex-shrink: 0;
    margin-right: 2rem;
}

.course-card.list-view .course-content {
    flex: 1;
    padding: 0;
}

.course-card.list-view .course-footer {
    padding: 0;
    margin-left: 2rem;
    flex-shrink: 0;
}

.course-image {
    width: 100%;
    height: 220px;
    position: relative;
    overflow: hidden;
}

.course-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-img {
    transform: scale(1.05);
}

.course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #9ca3af;
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, transparent 50%);
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 1rem;
}

.level-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.course-content {
    padding: 1.5rem;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-category {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.course-duration {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.course-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.feature i {
    color: #059669;
    width: 16px;
}

.course-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.info-item i {
    width: 14px;
    color: #f59e0b;
}

.start-course-btn {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.start-course-btn:hover {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

.card-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.course-card:hover .card-shine {
    left: 100%;
}

/* Lock Overlay Styles */
.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.lock-icon {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.lock-icon i {
    font-size: 1.5rem;
    color: #6b7280;
}

/* Locked Course Card Styles */
.course-card.locked {
    opacity: 0.8;
    cursor: pointer;
}

.course-card.locked:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.course-card.locked .course-img {
    filter: grayscale(20%);
}

.course-card.locked:hover .course-img {
    transform: scale(1.02);
}

.course-card.locked .start-course-btn {
    background: linear-gradient(135deg, #9ca3af, #6b7280);
    pointer-events: none;
}

.course-card.locked .start-course-btn:hover {
    background: linear-gradient(135deg, #9ca3af, #6b7280);
    transform: none;
    box-shadow: none;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .course-card.list-view {
        flex-direction: column;
        align-items: stretch;
    }
    
    .course-card.list-view .course-image {
        width: 100%;
        height: 180px;
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .course-card.list-view .course-content {
        padding: 0 0 1rem 0;
    }
    
    .course-card.list-view .course-footer {
        margin-left: 0;
        padding: 1rem 0 0 0;
        border-top: 1px solid #e5e7eb;
    }
    
    .course-image {
        height: 180px;
    }
    
    .course-content {
        padding: 1rem;
    }
    
    .course-footer {
        padding: 0 1rem 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .start-course-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .course-features {
        gap: 0.25rem;
    }
    
    .feature {
        font-size: 0.8rem;
    }
    
    .course-title {
        font-size: 1.125rem;
    }
    
    .start-course-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }

    /* Responsive Lock Icon */
    .lock-icon {
        width: 50px;
        height: 50px;
    }

    .lock-icon i {
        font-size: 1.25rem;
    }
}
