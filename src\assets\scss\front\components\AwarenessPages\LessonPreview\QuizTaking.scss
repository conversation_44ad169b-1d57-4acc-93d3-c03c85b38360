/* Quiz Taking */
.quiz-taking {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.quiz-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.quiz-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.quiz-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-info {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.question-counter {
    font-size: 1rem;
    font-weight: 600;
    color: #6b7280;
}

.progress-bar {
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #059669, #047857);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.quiz-content {
    padding: 2.5rem;
}

.question-slide {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.question-card {
    max-width: 700px;
    margin: 0 auto;
}

.question-header {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.question-number-badge {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.question-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.5;
    margin: 0;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-item {
    position: relative;
}

.option-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.option-label:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.option-label.selected {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.option-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.option-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    border: 2px solid #d1d5db;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: #6b7280;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.option-label.selected .option-indicator {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.option-text {
    font-size: 1rem;
    color: #374151;
    line-height: 1.5;
    flex: 1;
}

.option-label.selected .option-text {
    color: #1e40af;
    font-weight: 500;
}

.quiz-navigation {
    background: #f8fafc;
    padding: 2rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.nav-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.nav-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.nav-btn.disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.submit-btn {
    background: linear-gradient(135deg, #059669, #047857);
}

.submit-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #047857, #065f46);
    box-shadow: 0 6px 20px rgba(5, 150, 105, 0.3);
}

.nav-indicators {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #d1d5db;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-dot.active {
    background: #3b82f6;
    transform: scale(1.2);
}

.nav-dot.answered {
    background: #10b981;
}

.nav-dot:hover {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .quiz-content {
        padding: 1.5rem;
    }

    .quiz-navigation {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .nav-btn {
        width: 100%;
        max-width: 200px;
    }

    .question-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .option-label {
        padding: 1rem;
    }

    .nav-dot {
        width: 14px;
        height: 14px;
    }
}

/* Enhanced Animations */
.quiz-taking {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.question-slide {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States for Accessibility */
.nav-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

.option-label:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .quiz-taking,
    .option-label {
        border-width: 2px;
    }

    .option-indicator {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .quiz-taking,
    .question-slide {
        animation: none;
    }

    .option-label,
    .nav-btn,
    .nav-dot,
    .progress-fill {
        transition: none;
    }
}
