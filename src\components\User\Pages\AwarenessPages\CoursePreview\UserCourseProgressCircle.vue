<template>
  <div class="progress-overview">
    <div class="progress-circle" :style="{ '--progress': progress }">
      <svg viewBox="0 0 36 36" class="circular-chart">
        <path class="circle-bg"
          d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <path class="circle-progress"
          d="M18 2.0845
          a 15.9155 15.9155 0 0 1 0 31.831
          a 15.9155 15.9155 0 0 1 0 -31.831"
        />
      </svg>
      <div class="progress-text">{{ Math.round(progress) }}%</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  progress: { type: Number, required: true, default: 0 }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/UserCourseProgressCircle.scss';
</style>
