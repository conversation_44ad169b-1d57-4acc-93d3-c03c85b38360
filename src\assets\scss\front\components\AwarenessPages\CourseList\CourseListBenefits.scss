/* Benefits Section */
.benefits-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 24px;
    padding: 4rem 2rem;
    margin-top: 4rem;
}

.benefits-content {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.benefits-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 3rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.benefit-item {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1.5rem;
}

.benefit-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.benefit-item p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .benefits-section {
        padding: 3rem 1.5rem;
        margin-top: 3rem;
    }
    
    .benefits-title {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }
    
    .benefits-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .benefit-item {
        padding: 1.5rem;
    }
    
    .benefit-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .benefits-section {
        padding: 2rem 1rem;
        margin-top: 2rem;
        border-radius: 16px;
    }
    
    .benefits-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .benefit-item {
        padding: 1.25rem;
    }
    
    .benefit-item h3 {
        font-size: 1rem;
    }
    
    .benefit-item p {
        font-size: 0.875rem;
    }
}
