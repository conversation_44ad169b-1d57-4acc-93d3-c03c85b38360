graph TD
    A[Vue Router] --> B["/portal/awareness-evaluation"]
    A --> C["/portal/awareness-evaluation/course/all"]
    A --> D["/portal/awareness-evaluation/course/:course_id"]
    A --> E["/portal/awareness-evaluation/course/:course_id/topic/:topic_id/preview/:lesson_id"]
    
    B --> F[AwarenessEvaluationDashboard.vue]
    C --> G[CourseList.vue]
    D --> H[CoursePreview.vue]
    E --> I[LessonPreview.vue]
    
    F --> F1[Dashboard Components]
    G --> G1[CourseList Components]
    H --> H1[CoursePreview Components]
    I --> I1[LessonPreview Components]
    
    F1 --> F2[UserCourseCard.vue]
    F1 --> F3[CourseProgressBar.vue]
    
    G1 --> G2[CourseListCard.vue]
    G1 --> G3[CourseListStats.vue]
    G1 --> G4[CourseListBenefits.vue]
    
    H1 --> H2[UserCourseBreadcrumb.vue]
    H1 --> H3[UserCourseSidebar.vue]
    H1 --> H4[UserCourseOverviewStats.vue]
    H1 --> H5[UserCourseActionSection.vue]
    H1 --> H6[UserCourseProgressCircle.vue]
    
    I1 --> I2[LessonHeader.vue]
    I1 --> I3[LessonContent.vue]
    I1 --> I4[QuizTaking.vue]
    I1 --> I5[QuizResults.vue]
    
    style A fill:#e1f5fe
    style F fill:#c8e6c9
    style G fill:#c8e6c9
    style H fill:#c8e6c9
    style I fill:#c8e6c9
