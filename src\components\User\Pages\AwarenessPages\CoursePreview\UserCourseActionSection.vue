<template>
  <div class="course-actions">
    <div v-if="progress === 0" class="getting-started">
      <h3 class="getting-started-title"> <i class="fa fa-rocket"></i> Ready to start learning? </h3>
      <p class="getting-started-text"> Begin your journey by selecting the first topic from the sidebar and complete lessons to earn your certificate. </p>
      <button @click="handleStartLearning" class="start-learning-btn" v-if="hasAvailableLessons"> <i class="fa fa-play"></i> Start Learning </button>
    </div>

    <div v-else-if="progress < 100" class="continue-learning">
      <h3 class="continue-title"> <i class="fa fa-play-circle"></i> Continue Learning </h3>
      <p class="continue-text"> You've completed {{ completedCount }} out of {{ totalCount }} lessons. Keep going! </p>
      <button @click="handleContinueLearning" class="continue-btn"> <i class="fa fa-play"></i> Continue Course </button>
    </div>

    <div v-else class="course-completed">
      <h3 class="completed-title"> <i class="fa fa-trophy"></i> Congratulations! </h3>
      <p class="completed-text"> You've successfully completed this course. Your certificate is ready for download. </p>
      <button @click="handleDownloadCertificate" class="certificate-btn"> <i class="fa fa-certificate"></i> Download Certificate </button>
    </div>
  </div>
</template>

<script setup>
defineProps({
  progress: { type: Number, required: true },
  completedCount: { type: Number, required: true },
  totalCount: { type: Number, required: true },
  hasAvailableLessons: { type: Boolean, required: true }
});

const emit = defineEmits(['start-learning', 'continue-learning', 'download-certificate']);

const handleStartLearning = () => emit('start-learning');
const handleContinueLearning = () => emit('continue-learning');
const handleDownloadCertificate = () => emit('download-certificate');
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/UserCourseActionSection.scss';
</style>
