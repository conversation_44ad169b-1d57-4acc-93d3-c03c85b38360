/* Quiz Results */
.quiz-results {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.results-card {
    padding: 2.5rem;
}

.results-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Score Circle (inline) */
.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 4px solid;
    flex-shrink: 0;
}

.score-circle.excellent {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-color: #059669;
    color: #059669;
}

.score-circle.good {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-color: #3b82f6;
    color: #3b82f6;
}

.score-circle.average {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-color: #f59e0b;
    color: #d97706;
}

.score-circle.needs-improvement {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-color: #ef4444;
    color: #dc2626;
}

.score-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.score-label {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
}

.results-info {
    flex: 1;
}

.results-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.results-description {
    font-size: 1.1rem;
    color: #6b7280;
    margin: 0;
}

.answers-grid {
    margin-bottom: 2rem;
}

.answers-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.answers-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.answer-indicator {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    border: 2px solid;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-indicator.correct {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-color: #059669;
    color: #059669;
}

.answer-indicator.incorrect {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-color: #ef4444;
    color: #dc2626;
}

.answer-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.question-number {
    font-size: 0.625rem;
    margin-bottom: 2px;
}

.quiz-actions {
    display: flex;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.standard-quiz-actions,
.final-exam-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.passing-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.congratulations {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border: 1px solid #a7f3d0;
    border-radius: 12px;
    color: #059669;
    font-weight: 600;
}

.congratulations i {
    font-size: 1.5rem;
}

.failing-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.try-again-message {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #fbbf24;
    border-radius: 12px;
    color: #92400e;
    max-width: 400px;
}

.try-again-message i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.message-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #92400e;
}

.message-content p {
    margin: 0;
    line-height: 1.5;
}

.retake-btn,
.certificate-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.retake-btn:hover,
.certificate-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .results-card {
        padding: 1.5rem;
    }

    .results-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .score-circle {
        width: 100px;
        height: 100px;
    }

    .score-value {
        font-size: 1.5rem;
    }
}

/* Enhanced Animations */
.quiz-results {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.score-circle {
    animation: bounceIn 0.8s ease-out 0.3s both;
}

.answer-indicator {
    animation: fadeInUp 0.4s ease-out both;
}

.answer-indicator:nth-child(1) { animation-delay: 0.5s; }
.answer-indicator:nth-child(2) { animation-delay: 0.6s; }
.answer-indicator:nth-child(3) { animation-delay: 0.7s; }
.answer-indicator:nth-child(4) { animation-delay: 0.8s; }
.answer-indicator:nth-child(5) { animation-delay: 0.9s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Focus States for Accessibility */
.retake-btn:focus,
.certificate-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .quiz-results {
        border-width: 2px;
    }

    .score-circle,
    .answer-indicator {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .quiz-results,
    .score-circle,
    .answer-indicator {
        animation: none;
    }
}
