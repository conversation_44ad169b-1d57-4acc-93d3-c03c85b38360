<template>
  <div class="empty-state">
    <div class="empty-content">
      <div class="empty-illustration">
        <div class="main-icon" :style="iconStyle"> <i :class="icon"></i> </div>
        <div class="floating-elements">
          <div class="element element-1" :style="elementStyle"></div>
          <div class="element element-2" :style="elementStyle"></div>
          <div class="element element-3" :style="elementStyle"></div>
        </div>
      </div>
      <h2 class="empty-title">{{ title }}</h2>
      <p class="empty-description">{{ description }}</p>
      <div class="empty-actions" v-if="primaryAction || secondaryAction">
        <router-link v-if="primaryAction" :to="primaryAction.route" class="btn-primary large" :style="primaryButtonStyle">
          <i :class="primaryAction.icon" v-if="primaryAction.icon"></i>
          {{ primaryAction.text }}
        </router-link>
        <a v-if="secondaryAction" :href="secondaryAction.url" :target="secondaryAction.target || '_self'" class="btn-secondary" :style="secondaryButtonStyle">
          <i :class="secondaryAction.icon" v-if="secondaryAction.icon"></i>
          {{ secondaryAction.text }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: { type: String, required: true },
  description: { type: String, required: true },
  icon: { type: String, default: 'fa fa-inbox' },
  primaryAction: { type: Object, default: null },
  secondaryAction: { type: Object, default: null },
  primaryColor: { type: String, required: true },
  secondaryColor: { type: String, required: true }
});

const iconStyle = computed(() => ({
  background: `linear-gradient(135deg, ${props.primaryColor}20, ${props.primaryColor}40)`,
  color: props.primaryColor
}));

const elementStyle = computed(() => ({
  background: `linear-gradient(135deg, ${props.primaryColor}, ${props.secondaryColor})`
}));

const primaryButtonStyle = computed(() => ({
  background: `${props.primaryColor}33`,
  borderColor: `${props.primaryColor}4D`,
  color: props.primaryColor
}));

const secondaryButtonStyle = computed(() => ({
  borderColor: props.primaryColor,
  color: props.primaryColor
}));
</script>

<style scoped>
@import '@/assets/scss/general/EmptyState.scss';
</style>
