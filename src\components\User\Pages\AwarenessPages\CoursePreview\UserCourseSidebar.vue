<template>
  <aside class="course-sidebar">
    <div class="sidebar-header">
      <h3 class="sidebar-title"> <i class="fa fa-list-ul"></i> Course Content </h3>
      <UserCourseProgressCircle :progress="overallProgress" />
    </div>

    <div class="topics-container">
      <div class="topic-accordion">
        <div v-for="(topic, tIndex) in topics" :key="topic._id" class="topic-item">
          <div class="topic-header" :class="{ 'active': activeTopic === topic._id }" @click="toggleTopic(topic._id)">
            <div class="topic-info">
              <div class="topic-icon">
                <i class="fa fa-folder-o" v-if="activeTopic !== topic._id"></i>
                <i class="fa fa-folder-open-o" v-else></i>
              </div>
              <div class="topic-details">
                <h4 class="topic-title">{{ topic.title }}</h4>
                <span class="topic-lessons-count">{{ topic.lessons.length }} lessons</span>
              </div>
            </div>
            <div class="topic-actions">
              <div class="topic-progress"> <span class="progress-fraction">{{ getTopicProgress(topic) }}</span></div>
              <i class="fa fa-chevron-down expand-icon" :class="{ 'rotated': activeTopic === topic._id }"></i>
            </div>
          </div>

          <div class="topic-lessons" :class="{ 'expanded': activeTopic === topic._id }">
            <div v-if="topic.lessons.length === 0" class="no-lessons"> <i class="fa fa-info-circle"></i><span>No lessons available</span></div>
            <div v-else class="lessons-list">
              <div v-for="(lesson, lIndex) in topic.lessons" :key="lesson._id" class="lesson-item" :class="{ 'current': currentLesson === lesson._id, 'completed': lesson.completed === 1, 'locked': lesson.readable === 0 }">
                <router-link v-if="lesson.readable === 1" :to="{name: 'LessonPreview', params:{course_id: courseId, topic_id: topic._id, lesson_id: lesson._id}}" class="lesson-link">
                  <div class="lesson-status">
                    <i class="fa fa-circle-o" v-if="lesson.completed === 0"></i>
                    <i class="fa fa-check-circle" v-else></i>
                  </div>
                  <div class="lesson-content">
                    <span class="lesson-title">{{ lesson.title }}</span>
                    <div class="lesson-meta">
                      <span class="lesson-type" v-if="lesson.is_quiz === '1'"> <i class="fa fa-question-circle"></i> Quiz </span>
                      <span class="lesson-type" v-else> <i class="fa fa-play-circle"></i> Lesson </span>
                    </div>
                  </div>
                  <div class="lesson-indicator"> <i class="fa fa-play" v-if="currentLesson === lesson._id"></i></div>
                </router-link>
                
                <div v-else class="lesson-locked">
                  <div class="lesson-status"> <i class="fa fa-lock"></i> </div>
                  <div class="lesson-content">
                    <span class="lesson-title">{{ lesson.title }}</span>
                    <div class="lesson-meta"> <span class="lesson-type locked"> <i class="fa fa-lock"></i> Locked </span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
import UserCourseProgressCircle from './UserCourseProgressCircle.vue';

defineProps({
  topics: { type: Array, required: true },
  overallProgress: { type: Number, required: true },
  activeTopic: { type: String, default: null },
  currentLesson: { type: String, default: null },
  courseId: { type: String, required: true },
  getTopicProgress: { type: Function, required: true }
});

const emit = defineEmits(['toggle-topic']);
const toggleTopic = (topicId) => emit('toggle-topic', topicId);

</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/UserCourseSidebar.scss';
</style>
