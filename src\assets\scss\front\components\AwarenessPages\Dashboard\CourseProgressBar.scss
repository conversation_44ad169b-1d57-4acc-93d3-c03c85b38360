.progress-container {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.progress-text {
  display: block;
  text-align: center;
  font-size: 0.875rem;
  color: #059669;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
}

.progress-fill.progress-complete {
  background: linear-gradient(135deg, #059669, #047857);
}

.progress-fill.progress-good {
  background: linear-gradient(135deg, #10b981, #059669);
}

.progress-fill.progress-started {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.progress-fill.progress-none {
  background: #e5e7eb;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .progress-fill {
    transition: none;
  }
}

/* Print styles */
@media print {
  .progress-fill {
    background: #000 !important;
  }
}
