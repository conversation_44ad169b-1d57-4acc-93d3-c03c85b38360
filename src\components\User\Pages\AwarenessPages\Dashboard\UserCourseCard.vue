<template>
  <div class="course-card" :class="{ 'completed-course': isCompleted }" @click="handleCardClick">
    <div class="card-header">
      <div class="course-status">
        <div class="status-icon" :class="statusClass"> <i class="fa" :class="statusIcon"></i> </div>
        <span class="status-label">{{ statusLabel }}</span>
      </div>
      <div class="progress-badge"> {{ Math.round(course.completion_percentage || 0) }}% </div>
    </div>

    <div class="card-content">
      <h3 class="card-title">{{ course.title_short || course.title }}</h3>
      <p class="card-description" v-if="course.description_short || course.description"> {{ course.description_short || course.description }} </p>
        
      <CourseProgressBar
        :percentage="course.completion_percentage || 0"
        :completed-lessons="course.completed_lessons || 0"
        :total-lessons="course.total_lessons || 0"
        :progress-class="progressClass"
      />

      <div class="course-meta">
        <div class="meta-item"> <i class="fa fa-calendar"></i> <span>{{ formattedDate }}</span> </div>
        <div class="meta-item"> <i class="fa fa-clock-o"></i> <span>{{ course.duration || 'Self-paced' }}</span> </div>
      </div>
    </div>

    <div class="card-footer">
      <router-link v-if="!isCompleted" :to="{ name: 'AwarenessCoursePreview', params: { course_id: course._id } }" class="continue-btn">
        <i class="fa fa-play"></i> <span>Continue Learning</span>
      </router-link>

      <button v-else-if="course.certificate_eligible" @click="handleCertificateDownload" class="download-cert-btn">
        <i class="fa fa-download"></i> <span>Download Certificate</span>
      </button>

      <router-link v-else :to="`/portal/awareness-evaluation/course/${course._id}`" class="review-btn">
        <i class="fa fa-eye"></i> <span>Review Course</span>
      </router-link>
    </div>

    <div v-if="isCompleted" class="completion-ring">
      <div class="progress-circle">
        <img :src="completionRingSvg" alt="Completion Ring" class="circular-chart" />
        <div class="progress-text"> <i class="fa fa-check"></i> </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import CourseProgressBar from './CourseProgressBar.vue';
import completionRingSvg from '@/assets/svg/completion-ring.svg';

const props = defineProps({
  course: { type: Object, required: true },
  getStatusIcon: { type: Function, required: true },
  getStatusLabel: { type: Function, required: true },
  getStatusClass: { type: Function, required: true },
  getProgressClass: { type: Function, required: true },
  formatDate: { type: Function, required: true },
  navigateToCourse: { type: Function, required: true },
  downloadCourseCertificate: { type: Function, required: true }
});

const isCompleted = computed(() => (props.course.completion_percentage || 0) === 100);
const statusIcon = computed(() => props.getStatusIcon(props.course.completion_percentage));
const statusLabel = computed(() => props.getStatusLabel(props.course.completion_percentage));
const statusClass = computed(() => props.getStatusClass(props.course.completion_percentage));
const progressClass = computed(() => props.getProgressClass(props.course.completion_percentage || 0));
const formattedDate = computed(() => props.formatDate(props.course.last_activity || props.course.created_at));

const handleCardClick = () => { props.navigateToCourse(props.course) };

const handleCertificateDownload = (event) => {
  event.stopPropagation();
  props.downloadCourseCertificate(props.course._id);
};
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/Dashboard/CourseCard.scss';
</style>
