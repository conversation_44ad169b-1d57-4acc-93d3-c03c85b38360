.progress-overview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle {
  position: relative;
  width: 48px;
  height: 48px;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: #e5e7eb;
  stroke-width: 3;
}

.circle-progress {
  fill: none;
  stroke: #059669;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-dasharray: var(--progress, 0) 100;
  transition: stroke-dasharray 0.6s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: #059669;
}

/* Responsive Design */
@media (max-width: 480px) {
  .progress-circle {
    width: 36px;
    height: 36px;
  }
}
