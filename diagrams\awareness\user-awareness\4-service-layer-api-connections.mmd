graph TD
    A[useAwarenessActions] --> B[AwarenessService]
    
    B --> C[API Methods]
    C --> C1[getAwarenessEvaluations]
    C --> C2[getAvailableCourses]
    C --> C3[getSingleCourse]
    C --> C4[getCourseTopics]
    C --> C5[getSingleLesson]
    C --> C6[completeLesson]
    C --> C7[submitQuiz]
    C --> C8[downloadCourseCertificate]
    
    D[Backend API Endpoints] --> E[/api/portal/awareness]
    E --> E1[/evaluations]
    E --> E2[/get_courses]
    E --> E3[/single/:course_id]
    E --> E4[/:course_id/topic/list]
    E --> E5[/:course_id/topic/:topic_id/lesson/single/:lesson_id]
    E --> E6[/:course_id/topic/:topic_id/lesson/single/:lesson_id/complete]
    E --> E7[/:course_id/topic/:topic_id/lesson/single/:lesson_id/quiz]
    E --> E8[/single/:course_id/certificate]
    
    C1 --> E1
    C2 --> E2
    C3 --> E3
    C4 --> E4
    C5 --> E5
    C6 --> E6
    C7 --> E7
    C8 --> E8
    
    F[ApiService] --> G[HTTP Methods]
    G --> G1[GET]
    G --> G2[POST]
    G --> G3[GETPDF]
    
    B --> F
    
    H[awarenessApiRoutes.js] --> I[Route Definitions]
    I --> I1[AwarenessEvaluations]
    I --> I2[GetCourses]
    I --> I3[GetSingleCourse]
    I --> I4[GetTopicList]
    I --> I5[GetSingleLesson]
    I --> I6[CompleteLesson]
    I --> I7[SubmitQuiz]
    I --> I8[CourseCertificate]
    
    B --> H
    
    style A fill:#e8f5e8
    style B fill:#ffecb3
    style D fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#fff3e0
