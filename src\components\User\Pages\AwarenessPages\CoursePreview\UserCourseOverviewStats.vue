<template>
  <div class="course-stats">
    <div class="stat-item">
      <div class="stat-number">{{ topicsCount }}</div>
      <div class="stat-label">Topics</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">{{ lessonsCount }}</div>
      <div class="stat-label">Lessons</div>
    </div>
    <div class="stat-item">
      <div class="stat-number">{{ completedCount }}</div>
      <div class="stat-label">Completed</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  topicsCount: { type: Number, required: true },
  lessonsCount: { type: Number, required: true },
  completedCount: { type: Number, required: true }
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/CoursePreview/UserCourseOverviewStats.scss';
</style>
