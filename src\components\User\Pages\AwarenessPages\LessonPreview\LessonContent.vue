<template>
  <div class="lesson-content-wrapper">
    <div class="lesson-content-card">
      <div class="lesson-title-section">
        <h1 class="lesson-title">{{ lesson.title }}</h1>
        <div class="lesson-meta">
          <div class="meta-item"> <i class="fa fa-book"></i> </div>
          <div class="meta-item" v-if="lesson.completed === 1"> <i class="fa fa-check-circle completed-icon"></i> <span>Completed</span></div>
        </div>
      </div>
      
      <div class="lesson-content" v-if="lesson.is_quiz === '0'">
        <div class="content-body ql-editor" v-html="lesson.description"></div>
        
        <div class="lesson-actions" v-if="lesson.completed !== 1">
          <button @click="emit('complete')" class="complete-lesson-btn"> <i class="fa fa-check"></i>Mark as Complete</button>
        </div>
        
        <div class="completion-message" v-else>
          <div class="completion-card">
            <i class="fa fa-trophy completion-icon"></i>
            <h3>Lesson Completed!</h3>
            <p>Great job! You've successfully completed this lesson.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  lesson: { type: Object, required: true },
  courseId: { type: String, required: true },
  topicId: { type: String, required: true },
  lessonId: { type: String, required: true }
});

const emit = defineEmits(['complete']);
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/LessonPreview/LessonContent.scss';
</style>
